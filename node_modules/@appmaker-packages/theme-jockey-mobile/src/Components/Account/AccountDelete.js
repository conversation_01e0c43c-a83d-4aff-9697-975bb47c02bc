import { Alert, Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import Ripple from 'react-native-material-ripple';
import { fonts, heightPixel, widthPixel } from '../../styles';
import { useUser, useWishlistProductIds } from '@appmaker-xyz/shopify';
import { useNavigation } from '@react-navigation/native';
import { getSettings } from '../../../config';
import Snackbar from 'react-native-snackbar';
import { set } from 'lodash';
import { getIdFromGID } from '../../utils/Helper';

const AccountDelete = ({ onAction }) => {
    const { isLoggedin, user } = useUser();
    const customerId = getIdFromGID(user?.id);

    const navigation = useNavigation();
    const settings = getSettings();
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    myHeaders.append("version", settings.onboard_header_version);
    myHeaders.append("Origin", settings.onboard_header_origin);

    const { productIds } = useWishlistProductIds();

    const action = 'BATCH_REMOVE_WISHLIST';

    const onLogout = async () => {
        onAction({
            action: 'LOGOUT',
            params: {
                redirectAction: {
                    action: 'OPEN_INAPP_PAGE',
                    pageId: 'myaccountpage',
                }
            }
        })
        onAction({ action, params: { ids: productIds } });

        navigation.navigate("myaccountpage")
    }

    const eraseData = async () => {
        try {

            const requestOptions = {
                method: "DELETE",
                headers: myHeaders,
                redirect: "follow"
            };

            const eraseData = await fetch(`${settings.onboard_base_url}/api/shopify/customer/${customerId}?_action=erase`, requestOptions);

            const responseJson =
                // {
                //     data: null,
                //     message: "Your request for data erasure has been successfully submitted. Your personal data will be erased 180 days after your last order. You don't need to submit another request.",
                //     error: false,
                // }
                await eraseData.json();

            if (responseJson.error) {
                Snackbar.show({
                    text: 'Something went wrong! Please try again later.',
                    duration: Snackbar.LENGTH_SHORT,
                    backgroundColor: "#e74c3c"
                });

            } else if (responseJson.message === `Your request for data erasure has been successfully submitted. Your personal data will be erased 180 days after your last order. You don't need to submit another request.`) {
                Snackbar.show({
                    text: responseJson.message,
                    duration: Snackbar.LENGTH_SHORT,
                    backgroundColor: "#2ecc71"
                });
                setModalVisible(false);
            }

        } catch (error) {
            console.log("error", error);
            setModalVisible(false);
        }
    }

    const onDeleteAccount = async () => {
        const requestOptions = {
            method: "DELETE",
            headers: myHeaders,
            redirect: "follow"
        };


        try {
            const deleteResponse = await fetch(`${settings.onboard_base_url}/api/shopify/customer/${customerId}?_action=delete`, requestOptions);

            const responseJson =
                // {
                //     data: null,
                //     message: "Account can't be deleted as you have orders associated. You can still request to erase your personal data.",
                //     error: false,
                // }
                await deleteResponse.json();

            if (responseJson.error) {
                Snackbar.show({
                    text: 'Something went wrong! Please try again later.',
                    duration: Snackbar.LENGTH_SHORT,
                    backgroundColor: "#e74c3c"
                });
            } else {
                if (responseJson.message === 'Account will be deleted, and you will be logged out of the app.') {
                    Snackbar.show({
                        text: 'Account deleted successfully!',
                        duration: Snackbar.LENGTH_SHORT,
                        backgroundColor: "#2ecc71"
                    });

                    onLogout();
                } else if (responseJson.message === `Account can't be deleted as you have orders associated. You can still request to erase your personal data.`) {
                    setModalVisible(true);
                }
                // 
            }

        } catch (error) {
            console.log("error", error);
        }

    }

    const [modalVisible, setModalVisible] = useState(false);


    if (isLoggedin) {
        return (
            <View style={{ paddingBottom: heightPixel(30) }}>

                <Ripple style={styles.logoutButtonDelete} onPress={() => {
                    Alert.alert(
                        "Confirm Deletion",
                        "Are you sure you want to delete your account? This action cannot be undone.",
                        [
                            {
                                text: "Cancel",
                                style: "cancel",
                            },
                            {
                                text: "Delete",
                                onPress: onDeleteAccount,
                                style: "destructive",
                            },
                        ]
                    );
                }}>
                    <Text style={styles.logoutButtonTextDelete}>Delete Account</Text>
                </Ripple>

                <Modal
                    visible={modalVisible}
                    transparent
                    animationType="fade"
                    onRequestClose={() => setModalVisible(false)}
                >
                    <View style={styles.overlay}>
                        <View style={styles.modalContainer}>
                            <Text style={styles.message}>
                                {`Account can't be deleted as you have orders associated. You can still request to erase your personal data.`}
                            </Text>
                            <View style={styles.buttonContainer}>
                                <TouchableOpacity
                                    style={styles.eraseButton}
                                    onPress={() => {
                                        // Handle "Erase Data" action
                                        eraseData();
                                    }}
                                >
                                    <Text style={styles.eraseButtonText}>Erase data</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={styles.noButton}
                                    onPress={() => setModalVisible(false)}
                                >
                                    <Text style={styles.noButtonText}>No</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

            </View>

        )
    }
}

export default AccountDelete

const styles = StyleSheet.create({
    logoutButtonDelete: {
        height: heightPixel(43),
        justifyContent: "center",
        // alignItems: "center",
        width: "100%",
        alignSelf: "center",
        borderRadius: widthPixel(10),
        borderColor: "#000",
        borderWidth: .0,
        paddingHorizontal: widthPixel(16),
        marginTop: widthPixel(10),
    },
    logoutButton: {
        height: heightPixel(43),
        justifyContent: "center",
        alignItems: "center",
        width: "90%",
        alignSelf: "center",
        borderRadius: widthPixel(10),
        borderColor: "#000",
        borderWidth: 1,
        marginTop: widthPixel(10),
    },
    logoutButtonText: {
        fontSize: fonts._16,
        fontFamily: fonts.FONT_FAMILY.Regular,
        color: "#000000",
    },
    logoutButtonTextDelete: {
        fontSize: fonts._16,
        fontFamily: fonts.FONT_FAMILY.Regular,
        color: 'rgb(180, 163, 163)',
    },
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        width: '90%',
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 20,
        alignItems: 'center',
    },
    message: {
        fontSize: fonts._16,
        fontFamily: fonts.FONT_FAMILY.Medium,
        color: '#777',
        textAlign: 'center',
        marginBottom: 20,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
    },
    eraseButton: {
        flex: 1,
        height: 40,
        backgroundColor: '#fff',
        borderColor: '#7D7A30',
        borderWidth: 1,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    eraseButtonText: {
        color: '#7D7A30',
        fontSize: fonts._16,
        fontFamily: fonts.FONT_FAMILY.SemiBold,
    },
    noButton: {
        flex: 1,
        backgroundColor: '#7D7A30',
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noButtonText: {
        color: '#fff',
        fontSize: fonts._16,
        fontFamily: fonts.FONT_FAMILY.SemiBold,
    },
})