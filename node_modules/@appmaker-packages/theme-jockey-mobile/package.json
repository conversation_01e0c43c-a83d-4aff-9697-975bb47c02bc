{"name": "@appmaker-packages/theme-jockey-mobile", "version": "0.1.470", "main": "src/index.js", "description": "Appmaker", "dependencies": {"@react-navigation/material-top-tabs": "^6.6.14", "react-native-circular-progress": "1.4.0", "react-native-collapsible": "^1.6.2", "react-native-material-ripple": "^0.9.1", "react-native-responsive-screen": "^1.4.2", "react-native-snap-carousel": "3.9.1", "react-native-tab-view": "^3.5.2", "react-native-reanimated-carousel": "^3.5.1", "toggle-switch-react-native": "^3.3.0", "@twotalltotems/react-native-otp-input": "^1.3.11", "react-native-event-listeners": "^1.0.7", "react-native-shimmer-placeholder": "^2.0.9", "react-native-snapmint3": "^1.0.49"}, "peerDependencies": {"react-native-otp-verify": "^1.1.6", "@react-native-community/blur": "^4.4.1"}, "expo": {"ios": {"infoPlist": {"NSPhotoLibraryUsageDescription": "This app needs access to your photo library to allow you to upload and share images."}}}, "expo_plugins": ["@appmaker-packages/theme-jockey-mobile/app.plugin.js"]}