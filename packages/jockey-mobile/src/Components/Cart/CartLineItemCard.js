import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Animated,
  StyleSheet,
  Text,
  useWindowDimensions,
  View,
} from 'react-native';
import {
  Layout,
  ThemeText,
  AppTouchable,
  AppImage,
  StepperButton,
} from '@appmaker-xyz/ui';
import { currencyHelper } from '@appmaker-xyz/shopify';
import Icon from 'react-native-vector-icons/Feather';
import IconI from 'react-native-vector-icons/Ionicons';

import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Svg, { Circle, Path } from 'react-native-svg';
import { runDataSource } from '@appmaker-xyz/core';
import { Dropdown } from 'react-native-element-dropdown';
import { useCartActions, useCart } from '@appmaker-xyz/shopify';
import { Confirmations } from './Confirmations';
import {
  fonts,
  heightPixel,
  widthPixel,
  getHorizontalPadding,
} from '../../styles';
import Snackbar from 'react-native-snackbar';
import CustomDropdown from '../dropdown/CustomDropdown';
import FastImage from 'react-native-fast-image';
import ShopifyImage from '../ShopifyImage';

const dataSource = {
  attributes: {},
  source: 'shopify',
};

const CartLineItemCard = (props) => {
  const {
    title,
    imageUri,
    variationText,
    quantity,
    removeLoading,
    openProduct,
    canRemoveItem,
    price,
    compareAtPrice,
    currencyCode,
    customAttributes,
    handleValue,
    // onLineItemReplace,
    onLineItemDelete,
    id,
    isSelected,
    onSelect,
    onWishlistParent,
    userData,
    isDiscount = false,
    parentProps,
  } = props;
  const { manageCart, cartActionLoading } = useCartActions({});
  const [loading, setLoading] = useState(false);
  const [packLoading, setPackLoading] = useState(false);

  const [productDetails, setProductDetails] = useState({});
  const [showSameVariantText, setShowVariantText] = useState(false);

  const [packOfProduct, setPackOfProduct] = useState(null);

  const { lineItems } = useCart(parentProps);

  const [selectedSizeVariant, setSelectedSizeVariant] = useState({});
  const [selectedQuantity, setSelectedQuantity] = useState(quantity);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    getProductVariants();
  }, [variationText, id]);

  useEffect(() => {
    setSelectedQuantity(quantity);
  }, [quantity]);

  const getProductVariants = async () => {
    try {
      const [response] = await runDataSource(
        {
          dataSource,
        },
        {
          methodName: 'gqlQuery',
          params: {
            query: `
    {
productByHandle(handle: "${handleValue}") {
    handle
    title
    totalInventory
    tags
    id
    images(first: 10) {
      edges {
        node {
          url
        }
      }
    }
    metafields(identifiers: [{ key: "pack_of_product_number", namespace: "custom" }, { key: "colour_family", namespace: "custom" },{key:"pack_of_product_to_add",namespace:"custom"}]) {
    key  
    value
    }
      style_number: metafield(key: "style_number", namespace: "custom") {
         value
       }
      color_variant: metafield(key: "color_variant", namespace: "custom") {
      value
  }
    metafield(key: "color_variant", namespace: "custom") {
      value
      reference {
        ... on Product {
          images(first: 7) {
            nodes {
              url
              altText
            }
          }
          handle
          totalInventory
          title
          tags
          options(first: 10) {
            values
            name
          }
        }
      }
    }
    priceRange {
      minVariantPrice {
        amount
        currencyCode
      }
    }
    variants(first: 20) {
      edges {
        node {
          id
          sku
          title
          availableForSale
          quantityAvailable
          selectedOptions {
            name
            value
          }
        }
      }
    }
    options(first: 10) {
      values
      name
    }
}
}

    `,
          },
        },
      );

      let data = response?.data?.data?.productByHandle;

      const packOfProductNumber =
        data?.metafields?.find((item) => item?.key === 'pack_of_product_number')
          ?.value ?? null;
      const packOfProductToAdd =
        data?.metafields?.find((item) => item?.key === 'pack_of_product_to_add')
          ?.value ?? null;

      let updatedData = {
        ...data,
        variants: {
          edges: data?.variants?.edges
            ?.filter((edge) => edge?.node?.quantityAvailable > 0)
            ?.map((edge) => {
              if (edge?.node?.title === variationText) {
                setSelectedSizeVariant(edge?.node);
              }

              if (edge?.node?.quantityAvailable <= 0) {
                return undefined;
              }
              // Flatten the 'node' properties to the 'edge' level
              let { node } = edge;
              return { ...node }; // Keep the node properties directly in the edge
            }),
        },
      };

      if (updatedData.variants.edges.length === 0) {
        onLineItemDelete([id]);
      }

      setProductDetails(updatedData);

      if (packOfProductNumber != null) {
        const [packResponse] = await runDataSource(
          {
            dataSource,
          },
          {
            methodName: 'gqlQuery',
            params: {
              query: ` {
                    product(id: "${packOfProductToAdd}") {
                      id
                      priceRange {
                        maxVariantPrice {
                          amount
                          currencyCode
                        }
                      }
                        compareAtPriceRange {
                        maxVariantPrice {
                          amount
                          currencyCode
                        }
                      }
                        variants(first: 15) {
              nodes {
                id
                quantityAvailable
                title
              }
            }
                    }
                  }`,
            },
          },
        );

        setPackOfProduct({
          count: packOfProductNumber,
          id: packOfProductToAdd,
          price:
            packResponse?.data?.data?.product?.priceRange?.maxVariantPrice
              ?.amount,
          currencyCode:
            packResponse?.data?.data?.product?.priceRange?.maxVariantPrice
              ?.currencyCode,
          compareAtPrice:
            packResponse?.data?.data?.product?.compareAtPriceRange
              ?.maxVariantPrice?.amount,
          variants: packResponse?.data?.data?.product?.variants?.nodes,
        });
      } else {
        setPackOfProduct(null);
      }
    } catch (error) {
      console.log('error', error);
    } finally {
    }
  };

  const onLineItemUpdate = async (itemToUpdate) => {
    // console.log("itemToUpdate", itemToUpdate);

    setLoading(true);

    await manageCart({
      lineItemsToUpdate: itemToUpdate,
      // showMessage: true,
      updateCartPageStateRequired: true,
    });

    setLoading(false);
  };

  const toggleVisibility = () => {
    setShowVariantText(true),
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 1, // Fully visible
          duration: 700, // Fade-in duration
          useNativeDriver: true,
        }),
        Animated.delay(1000), // Delay before fading out (1 second)
        Animated.timing(opacity, {
          toValue: 0, // Fully hidden
          duration: 700, // Fade-out duration
          useNativeDriver: true,
        }),
      ]).start(() => {
        setShowVariantText(false); // Hide the text after animation completes
      });
  };

  const onLineItemReplace = async (itemsToAdd, itemToDelete) => {
    setPackLoading(true);
    // return;
    const foundItem = lineItems.find(
      (item) => item?.node?.variant?.id === itemsToAdd?.[0]?.variantId,
    );

    const updatedQuantity =
      foundItem?.node?.quantity + itemsToAdd?.[0]?.quantity;

    if (foundItem) {
      let lineItems = {
        id: foundItem?.node?.id,
        variantId: foundItem?.node?.variant?.id,
        quantity: updatedQuantity,
        customAttributes: foundItem?.node?.customAttributes,
      };

      await manageCart({
        lineItemsToUpdate: [lineItems],
        lineItemsToRemove: itemToDelete,
        updateCartPageStateRequired: true,
      });

      setSelectedQuantity(updatedQuantity);
      setPackLoading(false);
      setPackOfProduct(null);
      return;
    }

    await manageCart({
      lineItemsToAdd: itemsToAdd,
      lineItemsToRemove: itemToDelete,
      // showMessage: true,
      updateCartPageStateRequired: true,
    });

    setPackLoading(false);
    setPackOfProduct(null);
  };

  return (
    <View key={id}>
      <Layout
        style={[
          styles.container,
          { borderColor: isSelected ? '#000' : '#221f2014' },
        ]}>
        <View style={styles.rowContainer}>
          <AppTouchable onPress={openProduct} style={styles.imageContainer}>
            <AppTouchable onPress={onSelect} style={styles.absoluteIcon}>
              {isSelected ? (
                <Svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none">
                  <Circle cx="9" cy="9" r="9" fill="rgb(34, 31, 32)" />
                  <Path
                    d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978"
                    fill="rgb(34, 31, 32)"
                  />
                  <Path
                    d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </Svg>
              ) : (
                <Svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none">
                  <Circle cx="9" cy="9" r="9" fill="#CFCFCF" />
                  <Path
                    d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978"
                    fill="#CFCFCF"
                  />
                  <Path
                    d="M12.557 6.75781L7.9524 11.2311L5.85938 9.1978"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </Svg>
              )}
            </AppTouchable>

            <ShopifyImage
              source={{ uri: imageUri }}
              resizeMode={FastImage.resizeMode.contain}
              style={styles.imageStyles}
              maxWidth={200}
            />
          </AppTouchable>

          <Layout style={styles.contentContainer}>
            <Layout style={styles.titleContainer}>
              <ThemeText
                size="md"
                fontFamily="medium"
                style={styles.textShrink}
                numberOfLines={2}>
                {title}
              </ThemeText>
              {canRemoveItem ? (
                <AppTouchable
                  style={styles.deleteIcon}
                  disabled={removeLoading}
                  onPress={() => {
                    setShowConfirmation(true);
                  }}>
                  {removeLoading ? (
                    <ActivityIndicator
                      size={widthPixel(10)}
                      color="#212121"
                      style={{ borderWidth: 5 }}
                    />
                  ) : (
                    <IconI name="close" size={widthPixel(13)} color="#212121" />
                  )}
                </AppTouchable>
              ) : null}
            </Layout>
            {variationText ? (
              <Layout style={styles.rowBetweenContainer}>
                <CustomDropdown
                  data={productDetails?.variants?.edges}
                  selectedValue={selectedSizeVariant}
                  onValueChange={(item) => {
                    var cartQuantity = selectedQuantity;
                    if (item.id === selectedSizeVariant.id) {
                      toggleVisibility();
                      return;
                    }

                    const foundItem = lineItems.find((lineItem) => {
                      return lineItem?.node?.variant?.id === item.id;
                    });

                    if (foundItem) {
                      toggleVisibility();
                      return;
                    }
                    setSelectedSizeVariant(item);
                    if (cartQuantity > item?.quantityAvailable) {
                      setSelectedQuantity(1);
                      cartQuantity = 1;
                    }

                    const lineItem = [
                      {
                        id,
                        variantId: item?.id,
                        quantity: cartQuantity ?? quantity,
                        customAttributes,
                      },
                    ];
                    onLineItemUpdate(lineItem);
                  }}
                  labelField="title" // for size, it’s the title of the variant
                  valueField="title" // for size, we use the title as the unique value
                  placeholder="Update Size"
                  labelText="Size"
                />

                <CustomDropdown
                  data={(() => {
                    const arr = [];
                    let i = 1;
                    const quantityAvailable =
                      selectedSizeVariant?.quantityAvailable || 0;
                    while (i <= quantityAvailable && i <= 10) {
                      arr.push({ qty: i });
                      i++;
                    }
                    return arr;
                  })()}
                  selectedValue={{ qty: selectedQuantity }}
                  onValueChange={(item) => {
                    setSelectedQuantity(item.qty);
                    const lineItem = [
                      {
                        id,
                        variantId: selectedSizeVariant?.id,
                        quantity: item?.qty,
                        customAttributes,
                      },
                    ];
                    onLineItemUpdate(lineItem);
                  }}
                  labelField="qty" // for quantity, it's the qty field
                  valueField="qty" // for quantity, we use qty to select the value
                  placeholder="Update Qty"
                  labelText="Qty"
                />
                <View
                  style={{ alignItems: 'center', justifyContent: 'center' }}>
                  {loading && <ActivityIndicator color="#212121" />}
                </View>
              </Layout>
            ) : null}

            {compareAtPrice > price ? (
              <>

                <View
                  style={[
                    styles.priceAndStyleContainer,
                  ]}>

                  <Text style={styles.priceTextTotal}>
                    {currencyHelper(price, currencyCode)}
                  </Text>

                  <Text style={styles.strikePrice}>
                    {currencyHelper(compareAtPrice, currencyCode)}
                  </Text>

                </View>

                <View style={styles.styleContainer}>

                  <Text style={styles.savedAmount}>
                    Saved ₹{compareAtPrice - price}
                  </Text>

                  <View style={{ flexDirection: 'row', }}>

                    <Text style={styles.styleLabel}>Style: </Text>
                    <Text style={styles.styleValue}>
                      {productDetails?.style_number?.value}{' '}
                    </Text>

                  </View>


                </View>

              </>

            ) : (
              <View style={[styles.priceAndStyleContainer]}>
                <Text style={styles.priceTextTotal}>
                  {currencyHelper(price, currencyCode)}
                </Text>

                <View style={styles.styleContainerSingle}>
                  <Text style={styles.styleLabel}>Style: </Text>
                  <Text style={styles.styleValue}>
                    {productDetails?.style_number?.value}{' '}
                  </Text>
                </View>
              </View>
            )}

            {userData?.tags?.includes('jockey-employee') && isDiscount && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: widthPixel(5),
                  paddingVertical: heightPixel(4),
                }}>
                <Svg
                  aria-hidden="true"
                  focusable="false"
                  class="icon icon-discount color-foreground-text"
                  height={widthPixel(12)}
                  width={widthPixel(12)}>
                  <Path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M7 0h3a2 2 0 012 2v3a1 1 0 01-.3.7l-6 6a1 1 0 01-1.4 0l-4-4a1 1 0 010-1.4l6-6A1 1 0 017 0zm2 2a1 1 0 102 0 1 1 0 00-2 0z"
                    fill="#000"></Path>
                </Svg>

                <Text>Employee Benefits</Text>
              </View>
            )}

            {showSameVariantText && (
              <Animated.View
                style={[
                  styles.alreadySelectedContainer,
                  {
                    opacity: opacity,
                  },
                ]}>
                <Text style={styles.alreadySelectedText}>
                  {'This variant is already added.'}
                </Text>

                <Svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="11"
                  height="11"
                  viewBox="0 0 38 38"
                  fill="none">
                  <Path
                    d="M32.3016 5.69961C25.0016 -1.60039 13.0016 -1.60039 5.70156 5.69961C-1.59844 12.9996 -1.59844 24.9996 5.70156 32.2996C9.40156 35.9996 14.2016 37.7996 19.0016 37.7996C23.8016 37.7996 28.6016 35.9996 32.3016 32.2996C39.6016 24.9996 39.6016 12.9996 32.3016 5.69961ZM30.9016 30.8996C24.4016 37.3996 13.7016 37.3996 7.20156 30.8996C0.701563 24.3996 0.701563 13.6996 7.20156 7.19961C10.5016 3.89961 14.8016 2.29961 19.1016 2.29961C23.4016 2.29961 27.7016 3.89961 31.0016 7.19961C37.4016 13.6996 37.4016 24.2996 30.9016 30.8996ZM19.6016 16.3996C19.8016 16.5996 20.0016 16.8996 20.0016 17.2996V26.8996C20.0016 27.2996 19.9016 27.5996 19.6016 27.7996C19.3016 27.9996 19.1016 28.0996 18.7016 28.0996C18.4016 28.0996 18.1016 27.9996 17.8016 27.7996C17.6016 27.5996 17.4016 27.2996 17.4016 26.8996V17.2996C17.4016 16.8996 17.5016 16.5996 17.8016 16.3996C18.1016 16.1996 18.3016 16.0996 18.7016 16.0996C19.1016 16.0996 19.4016 16.1996 19.6016 16.3996ZM19.9016 10.9996C20.2016 11.2996 20.3016 11.5996 20.3016 11.9996C20.3016 12.3996 20.2016 12.7996 19.9016 12.9996C19.6016 13.2996 19.3016 13.3996 18.8016 13.3996C18.3016 13.3996 18.0016 13.2996 17.7016 12.9996C17.4016 12.6996 17.3016 12.3996 17.3016 11.9996C17.3016 11.5996 17.4016 11.1996 17.7016 10.9996C18.0016 10.6996 18.4016 10.5996 18.8016 10.5996C19.2016 10.5996 19.6016 10.6996 19.9016 10.9996Z"
                    fill="black"></Path>
                </Svg>
              </Animated.View>
            )}
          </Layout>
        </View>

        {packOfProduct != null &&
          selectedQuantity < packOfProduct?.count &&
          packOfProduct?.variants?.find(
            (variant) => variant?.title === selectedSizeVariant.title,
          ).quantityAvailable > 0 && (
            <View style={styles.packOfProductcontainer}>
              <View style={styles.infoContainer}>
                <Svg
                  xmlns="http://www.w3.org/2000/svg"
                  width={24}
                  height={29}
                  viewBox="0 0 24 29"
                  fill="none">
                  <Path
                    d="M20.6806 11.7916H10.6231C11.0882 11.257 11.3708 10.5597 11.3708 9.79713C11.3708 8.11907 10.0056 6.75391 8.32754 6.75391C6.64948 6.75391 5.28431 8.11907 5.28431 9.79713C5.28431 10.5597 5.56689 11.257 6.03191 11.7916H3.63109C2.18031 11.7916 1 12.9719 1 14.4227V17.5273C1 17.818 1.23559 18.0535 1.52622 18.0535C1.81685 18.0535 2.05244 17.818 2.05244 17.5273V14.4227C2.05244 13.5522 2.76062 12.844 3.63109 12.844H18.3652C19.2357 12.844 19.9439 13.5522 19.9439 14.4227V17.2643H14.4712C13.0204 17.2643 11.8401 18.4446 11.8401 19.8954C11.8401 21.3461 13.0204 22.5265 14.4712 22.5265H19.9439V25.368C19.9439 26.2385 19.2357 26.9467 18.3652 26.9467H3.63109C2.76062 26.9467 2.05244 26.2385 2.05244 25.368V22.2592C2.05244 21.9686 1.81685 21.733 1.52622 21.733C1.23559 21.733 1 21.9686 1 22.2592V25.368C1 26.8188 2.18031 27.9991 3.63109 27.9991H20.6806C22.1313 27.9991 23.3116 26.8188 23.3116 25.368V14.4227C23.3116 12.9719 22.1313 11.7916 20.6806 11.7916ZM8.32754 7.8064C9.42528 7.8064 10.3183 8.69944 10.3183 9.79718C10.3183 10.8949 9.42528 11.788 8.32754 11.788C7.22984 11.788 6.33675 10.8949 6.33675 9.79718C6.33675 8.69944 7.22979 7.8064 8.32754 7.8064ZM20.4687 12.844H20.6806C21.551 12.844 22.2592 13.5522 22.2592 14.4227V17.2643H20.9963V14.4227C20.9963 13.8309 20.7997 13.2841 20.4687 12.844ZM12.8925 19.8954C12.8925 19.0249 13.6007 18.3167 14.4712 18.3167H22.2592V21.474H14.4712C13.6007 21.474 12.8925 20.7658 12.8925 19.8954ZM20.6806 26.9467H20.4687C20.7997 26.5066 20.9963 25.9599 20.9963 25.368V22.5265H22.2592V25.368C22.2592 26.2385 21.551 26.9467 20.6806 26.9467Z"
                    fill="#221F20"
                    fillOpacity="0.85"
                  />
                  <Path
                    d="M20.6806 11.7416H10.7304C11.1615 11.21 11.4208 10.5335 11.4208 9.79713C11.4208 8.09146 10.0332 6.70391 8.32754 6.70391C6.62186 6.70391 5.23431 8.09146 5.23431 9.79713C5.23431 10.5335 5.49356 11.21 5.92462 11.7416H3.63109C2.15269 11.7416 0.95 12.9443 0.95 14.4227V17.5273C0.95 17.8456 1.20797 18.1035 1.52622 18.1035C1.84446 18.1035 2.10244 17.8456 2.10244 17.5273V14.4227C2.10244 13.5798 2.78823 12.894 3.63109 12.894H18.3652C19.2081 12.894 19.8939 13.5798 19.8939 14.4227V17.2143H14.4712C12.9928 17.2143 11.7901 18.417 11.7901 19.8954C11.7901 21.3738 12.9928 22.5765 14.4712 22.5765H19.8939V25.368C19.8939 26.2109 19.2081 26.8967 18.3652 26.8967H3.63109C2.78823 26.8967 2.10244 26.2109 2.10244 25.368V22.2592C2.10244 21.941 1.84446 21.683 1.52622 21.683C1.20797 21.683 0.95 21.941 0.95 22.2592V25.368C0.95 26.8464 2.15269 28.0491 3.63109 28.0491H20.6806C22.159 28.0491 23.3616 26.8464 23.3616 25.368V14.4227C23.3616 12.9443 22.159 11.7416 20.6806 11.7416ZM8.32754 7.8564C9.39767 7.8564 10.2683 8.72705 10.2683 9.79718C10.2683 10.8673 9.39767 11.738 8.32754 11.738C7.25746 11.738 6.38675 10.8673 6.38675 9.79718C6.38675 8.72705 7.25741 7.8564 8.32754 7.8564ZM21.0463 14.4227C21.0463 13.855 20.8688 13.328 20.5666 12.894H20.6806C21.5234 12.894 22.2092 13.5798 22.2092 14.4227V17.2143H21.0463V14.4227ZM12.9425 19.8954C12.9425 19.0525 13.6283 18.3667 14.4712 18.3667H22.2092V21.424H14.4712C13.6283 21.424 12.9425 20.7382 12.9425 19.8954ZM20.6806 26.8967H20.5666C20.8688 26.4628 21.0463 25.9358 21.0463 25.368V22.5765H22.2092V25.368C22.2092 26.2109 21.5234 26.8967 20.6806 26.8967Z"
                    stroke="#221F20"
                    strokeOpacity="0.85"
                    strokeWidth={0.1}
                  />
                  <Path
                    d="M14.3309 19.3711C14.192 19.3711 14.0562 19.4274 13.9583 19.5253C13.8605 19.6232 13.8047 19.7589 13.8047 19.8973C13.8047 20.0357 13.8605 20.1715 13.9583 20.2693C14.0567 20.3672 14.192 20.4235 14.3309 20.4235C14.4693 20.4235 14.6045 20.3672 14.7029 20.2693C14.8008 20.1715 14.8571 20.0357 14.8571 19.8973C14.8571 19.7589 14.8008 19.6232 14.7029 19.5253C14.6051 19.4273 14.4693 19.3711 14.3309 19.3711Z"
                    fill="#221F20"
                    fillOpacity="0.85"
                    stroke="#221F20"
                    strokeOpacity="0.85"
                    strokeWidth={0.2}
                  />
                  <Path
                    d="M15.9807 8.24661C17.6588 8.24661 19.0239 6.88144 19.0239 5.20338C19.0239 3.52538 17.6588 2.16016 15.9807 2.16016C14.3027 2.16016 12.9375 3.52532 12.9375 5.20338C12.9375 6.88144 14.3027 8.24661 15.9807 8.24661ZM15.9807 3.21265C17.0784 3.21265 17.9715 4.10569 17.9715 5.20343C17.9715 6.30118 17.0785 7.19422 15.9807 7.19422C14.883 7.19422 13.9899 6.30118 13.9899 5.20343C13.9899 4.10569 14.883 3.21265 15.9807 3.21265Z"
                    fill="#428BC1"
                    stroke="#428BC1"
                    strokeWidth={0.2}
                  />
                  <Path
                    d="M10.8465 6.18751C11.1372 6.18751 11.3727 5.95192 11.3727 5.66129V1.52622C11.3727 1.23559 11.1372 1 10.8465 1C10.5559 1 10.3203 1.23559 10.3203 1.52622V5.66129C10.3203 5.95192 10.5559 6.18751 10.8465 6.18751Z"
                    fill="#428BC1"
                    stroke="#428BC1"
                    strokeWidth={0.2}
                  />
                  <Path
                    d="M8.31528 4.3678C8.60591 4.3678 8.8415 4.13221 8.8415 3.84158V1.52622C8.8415 1.23559 8.60591 1 8.31528 1C8.02465 1 7.78906 1.23559 7.78906 1.52622V3.84158C7.78906 4.13221 8.02465 4.3678 8.31528 4.3678Z"
                    fill="#428BC1"
                    stroke="#428BC1"
                    strokeWidth={0.2}
                  />
                  <Path
                    d="M1.52622 20.4235C1.66461 20.4235 1.80038 20.3672 1.89825 20.2693C1.99613 20.1715 2.05244 20.0357 2.05244 19.8973C2.05244 19.7589 1.99613 19.6232 1.89825 19.5253C1.80038 19.4274 1.66514 19.3711 1.52622 19.3711C1.38782 19.3711 1.25206 19.4274 1.15418 19.5253C1.05631 19.6232 1 19.7589 1 19.8973C1 20.0357 1.05631 20.1715 1.15418 20.2693C1.25206 20.3672 1.38777 20.4235 1.52622 20.4235Z"
                    fill="#221F20"
                    fillOpacity="0.85"
                  />
                  <Path
                    d="M1.52622 20.5235C1.69117 20.5235 1.85236 20.4567 1.96897 20.3401C2.08557 20.2235 2.15244 20.0623 2.15244 19.8973C2.15244 19.7324 2.08557 19.5712 1.96897 19.4546C1.8524 19.338 1.69175 19.2711 1.52622 19.2711C1.36127 19.2711 1.20008 19.338 1.08347 19.4546C0.966867 19.5712 0.9 19.7324 0.9 19.8973C0.9 20.0623 0.966867 20.2235 1.08347 20.3401C1.20007 20.4567 1.36121 20.5235 1.52622 20.5235Z"
                    stroke="#221F20"
                    strokeOpacity="0.85"
                    strokeWidth={0.2}
                  />
                </Svg>
                {getHorizontalPadding(15)}

                {packOfProduct?.compareAtPrice > packOfProduct?.price ? (
                  <View style={styles.textContainer}>
                    <Text style={styles.buyText}>Buy</Text>
                    <Text style={styles.packText}>
                      {' '}
                      Pack of {packOfProduct?.count}
                    </Text>
                    <Text style={styles.buyText}> and </Text>
                    <Text style={styles.priceText}>{`save `}</Text>
                    <Text style={styles.priceText}>{`${currencyHelper(
                      packOfProduct?.compareAtPrice - packOfProduct?.price,
                      packOfProduct?.currencyCode,
                    )}`}</Text>
                  </View>
                ) : (
                  <View style={styles.textContainer}>
                    <Text style={styles.buyText}>Buy</Text>
                    <Text style={styles.packText}>
                      {' '}
                      Pack of {packOfProduct?.count} @
                    </Text>
                    <Text style={styles.priceText}>
                      {currencyHelper(
                        packOfProduct?.price,
                        packOfProduct?.currencyCode,
                      )}
                    </Text>
                  </View>
                )}

                <View style={styles.replaceContainer}>
                  <AppTouchable
                    onPress={() => {
                      let variant = packOfProduct?.variants?.find(
                        (variant) => variant?.title === selectedSizeVariant.title,
                      );

                      let lineItem = [
                        {
                          variantId: variant?.id,
                          quantity: 1,
                          customAttributes: customAttributes,
                        },
                      ];

                      onLineItemReplace(lineItem, [id]);
                    }}>
                    {packLoading ? (
                      <ActivityIndicator color="#1B1B1B" />
                    ) : (
                      <Text style={styles.replaceText}>REPLACE</Text>
                    )}
                  </AppTouchable>
                </View>
              </View>
            </View>
          )}
      </Layout>

      {showConfirmation && (
        <Confirmations
          onClose={() => setShowConfirmation(false)}
          onWishlist={() => {
            onWishlistParent();
          }}
          id={id}
        />
      )}

      {/* {loading && (
                <View style={styles.overlay}>
                    <ActivityIndicator size="large" color="#fff" />
                </View>
            )} */}
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject, // Covers the entire screen
    backgroundColor: 'rgba(0, 0, 0, 0.2)', // Semi-transparent black background
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  container: {
    marginTop: heightPixel(12),
    backgroundColor: 'white',
    borderRadius: widthPixel(12),
    elevation: 1,
    shadowColor: '#000',
    shadowOpacity: 0.1, // Adjust shadow visibility
    shadowOffset: { width: 0, height: widthPixel(2) },
    borderWidth: 1,
  },
  rowContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  imageContainer: {
    marginHorizontal: widthPixel(8),
    overflow: 'hidden',
    marginVertical: heightPixel(8),
    borderRadius: widthPixel(12),
    width: 122,
    height: 149,
  },
  absoluteIcon: {
    position: 'absolute',
    zIndex: 1,
    top: 12,
    left: 12,
  },
  imageStyles: {
    height: '100%',
    width: '100%',
  },
  titleContainer: {
    gap: widthPixel(8),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  deleteIcon: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#dadada',
    borderRadius: widthPixel(16),
    height: widthPixel(18),
    width: widthPixel(18),
    alignItems: 'center',
    justifyContent: 'center',
  },
  textShrink: {
    flexShrink: 1,
    fontSize: fonts._12,
    fontFamily: fonts.FONT_FAMILY.Medium,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  regularPrice: {
    marginRight: widthPixel(4),
    textDecorationLine: 'line-through',
  },
  stepperContainer: {
    borderRadius: widthPixel(2),
    borderColor: '#E9ECF3',
  },
  qtyDropDown: {
    width: 100,
    height: heightPixel(30),
    borderWidth: 0.7,
    borderColor: '#221f20',
    borderRadius: widthPixel(6),
    paddingHorizontal: widthPixel(8),
    alignItems: 'center',
  },
  sizDropDown: {
    width: 200,
    height: widthPixel(30),
    borderWidth: 0.7,
    borderColor: '#221f20',
    borderRadius: widthPixel(6),
    paddingHorizontal: widthPixel(8),
    // alignItems: "center",
    // justifyContent: 'center'
  },
  rowBetweenContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: widthPixel(10),
    marginTop: widthPixel(8),
  },
  dropDownText: {
    fontSize: fonts._12,
    fontWeight: '500',
    color: '#221f20',
    fontStyle: fonts.FONT_FAMILY.Medium,
  },

  startText: {
    color: '#221f20bf',
    fontSize: fonts._10,
    fontStyle: fonts.FONT_FAMILY.Medium,
    paddingRight: widthPixel(8),
  },
  contentContainer: {
    flex: 1,
    paddingVertical: widthPixel(8),
    marginEnd: widthPixel(8),
    marginStart: widthPixel(4),
  },
  priceAndStyleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: widthPixel(8),
    gap: widthPixel(8),
    flexWrap: 'wrap',
  },
  priceTextTotal: {
    fontSize: fonts._14,
    fontFamily: fonts.FONT_FAMILY.SemiBold,
  },
  strikePrice: {
    fontSize: fonts._14,
    fontFamily: fonts.FONT_FAMILY.Regular,
    textDecorationLine: 'line-through',
  },
  savedAmount: {
    color: '#428bc1',
    fontFamily: fonts.FONT_FAMILY.SemiBold,
    fontSize: fonts._12,
  },
  packOfProductcontainer: {
    backgroundColor: 'white',
    borderRadius: widthPixel(8),
    flexDirection: 'row',
    borderColor: 'grey',
    padding: widthPixel(8),
    marginHorizontal: widthPixel(8),
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    marginVertical: heightPixel(8),
    elevation: 4,
  },
  styleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  styleContainerSingle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  styleLabel: {
    fontSize: fonts._11,
    fontWeight: '300',
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  infoContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  styleValue: {
    fontSize: fonts._11,
    fontWeight: '700',
    fontFamily: fonts.FONT_FAMILY.Bold,
  },
  alreadySelectedContainer: {
    marginVertical: heightPixel(5),
    padding: widthPixel(3),
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
    borderRadius: widthPixel(5),
    paddingHorizontal: heightPixel(8),
    flexDirection: 'row',
    backgroundColor: '#8080804d',
  },
  alreadySelectedText: {
    fontSize: fonts._12,
    fontFamily: fonts.FONT_FAMILY.Regular,
    paddingRight: 5,
  },
  textContainer: {
    flex: 1, flexDirection: 'row',
    flexWrap: 'wrap'
  },
  buyText: {
    fontSize: fonts._13,
    fontWeight: '400',
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  packText: {
    fontSize: fonts._13,
    fontWeight: '700',
    fontFamily: fonts.FONT_FAMILY.SemiBold,
  },
  priceText: {
    fontSize: fonts._13,
    color: '#428bc1',
    fontFamily: fonts.FONT_FAMILY.SemiBold,
  },
  replaceContainer: {
    borderWidth: 1,
    height: heightPixel(31),
    // paddingHorizontal: widthPixel(16),
    width: widthPixel(100),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: widthPixel(8),
  },
  replaceText: {
    fontSize: fonts._12,
    fontWeight: '500',
    fontFamily: fonts.FONT_FAMILY.Regular,
    color: 'black',
  },
});

export default CartLineItemCard;
